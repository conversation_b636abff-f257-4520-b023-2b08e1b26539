import { GetDepartment<PERSON><PERSON> } from './department'
import {
  GetDictionaryDetailEnumListApi,
  GetEducationLevelListApi,
  GetEmployeeDutyListApi,
  GetMaritalStatusListApi,
} from './employees'
import {
  Adminbusiness_unitcredit_levellist,
  Adminbusiness_unitsale_arealist,
  Adminbusiness_unitsale_grouplist,
  Adminemployeelist,
  AdminenumsettleType,
  AdmingetUserDropdownList,
  AdminsaleSystemgetSaleSystemDropdownList,
  AdminuenumsupplierType,
  AdminuenumwarehouseType,
  AdminusergetSaleUserDropdownList,
  CreditLevel,
  GetRadixPointSignApi,
  GetSaleSystemDropdownListApi,
  GetSaleSystemDropdownListV2,
  GetSettleCycleApi,
  GetSettleTypeApi,
  GetUserDropdownList,
  Quoterojectenum,
  RecExpType,
  StatusListApi,
} from './marketingSystem'
import { GetBusinessUnitListApi } from './purchaseGreyFarbric'
import { GetGawMaterialListMenu, GetInfoBaseRawMaterialLevelEnumList } from './rawMatableterialInventory'
import {
  BusinessUnitSupplierEnumAll,
  BusinessUnitSupplierEnumlist,
  GetDyeingColorDetailsByProduct,
  GetDyeingColorDetailsByProductListEnum,
  GetGfmWarehouseTypeEnum,
  GetGreyFabricInfoListUseByOthers,
  GetInfoDyeingFinishingProgressEnumList,
  GetInfoSaleTaxableItemEnumList,
  GetListFactory,
  GetPlanTypeReverseIntMap,
  GetPostageItemsReverseIntMap,
  GetPurchaseGreyFabricItemList,
  GetSendProductTypeReverseIntMap,
  GetSettleTypeReverseIntMap,
  GetTypeCapitalExpensesEnumList,
  GetTypeSettleAccountsEnumList,
  Getdnfchargingmethodenum,
  GetlistFactoryEnum,
  Logistics_enum_list,
  PushType,
  business_unitlist,
  business_unitsupplierlist,
  getAuditStatusEnums,
  getFinishProductColorDropdownList,
  getInfoBaseGreyFabricLevelList,
  getInfoBaseMeasurementUnitEnumList,
  getInfoBaseMeasurementUnitList,
  getInfoDyeingFinishingProcessDataEnumList,
  getInfoProductGrayFabricColorList,
  getInfoProductLoomModelList,
  getInfoProductWeaveSpecificationList,
  getInfoSaleOrderCategoryEnumList,
  getProductionNotifyOrderList,
  getSettleTypeEnum,
  getShowTypeEnum,
  getSourceWarehouseTypeEnum,
  getTypeFabricList,
  getTypeGreyFabricOrderList,
  getTypeWarehouseList,
  rawmaterialMenu,
} from './settlementType'
import { DnfChargingMethodEnum } from './supplier'
import { RoleAccessDataScope } from './user'

import {
  GetAuditStatusEnum,
  GetCustomerEnumList,
  GetInfoPurchaseInvoiceHeaderListUseByOther,
} from './blanketManagement'
import { GetInfoBaseGreyFabricLevelListUseByOther } from './fabricLevel'
import {
  GetFinishProductColorDropdownList,
  GetInfoBaseFinishedProductLevelEnumList,
  GetProductColorKindByProductId,
  GetTypeFinishedProductColorEnumList,
} from './finishedProductColorInformation'
import { GetFinishProductDropdownList, GetPhysicalWarehouseDropdownList } from './finishedProductInformation'
import { GetPhysicalWarehouseBinListEnum, GetTypeFabricEnumList } from './finishPurchaseWarehouseEntry'
import { GetInfoSaleLogisticsCompanyEnumList } from './fpInteriorAllotDeliverFromGodownOrder'
import { GetWarehouseGoodOutEnumTypeReverseIntMap } from './fpPurchaseReturnDeliverGodown'
import { FactoryLogisticsEnum } from './fpSaleDeliverFromGodownOrder'
import { GetEmployeeListEnum } from './fpStockAdjustOrder'
import { GetWarehouseGoodInTypeEnum } from './fpSubscribeWarehouseOrder'
import { GetGreyFabricInfoListUseByOthersMenu } from './greyFabricInformation'
import { GetDataType, PrintTemplateType } from './print'
import {
  GetInfoProductPaymentTermList,
  GetProductionNotifyOrderDropdownList,
  GetProductionPlanOrderEsc,
} from './productionNotice'
import { PlanTypeDropdownList } from './sheetOfProductionPlan'

import { GetInfoSaleSettlementMethodEnumList } from './advancesReceived'
import { GetWarehouseGoodOutTypeEnum } from './cashCommodityClothOrder'
import { GetEnumBizUnitType } from './contactUnit'
import { OweStatus } from './customerArrearage'
import { GetStockShowType, GetStockStatusEnum } from './finishedGoodsInventory'
import { GetProcessOutTypeTypeEnum } from './fpProcessingDeliverFromGodownOrder'
import { CollectStatus, CollectType, GetEnumWriteOffStatusReverseIntMap } from './fundsReceived'
import { GetRawMaterialColor, GetRawMaterialEditOrderTypeEnum } from './rawStockTable'
import { GetWarehouseGoodOutChangeTypeEnum } from './saleCashCommodityClothChangeOrder'
import { GetSaleLevelDropdownList } from './salePriceManagement'
import { GetListByDyelotNumber } from './qualityInspectionReport'
import { getPhysicalWarehouseListTreeEnum } from './clothType'
import { GetInfoSaleShipmentTypeList } from './files/basicData/saleslssueType'
import { GetGfmWarehouseOutTypeEnum } from './warehouse'
import { GetCarouselBannerJumpType, GetCarouselBannerStatus } from './colorCardManagement/banner'
import { GetDropdownListWithoutDS } from '@/api/warehouseInformation'
import { GetRoleDropdownList } from '@/api/roles'
import { GetPayStatus } from '@/api/supplierBalanceSheet'
import { GetPayStatusEnum, GetPayWayEnum } from '@/api/payRecord'
import {
  GetBusinessStatusEnumDropdownList,
  GetDNFTypeEnum,
  GetDnFSituStatusReverseIntMap,
  GetOrderTypeEnum,
  GetReturnSupplierTypeReverseIntMap,
  GetSituStatusReverseIntMap,
  GetSrcTypeEnum,
  GetTenantManagementStateEnum,

} from '@/api'
import { getPhysicalWarehouseList } from '@/api/shrink'
import { GetGfmCheckOrderTypeEnum } from '@/api/dyeingFactoryTable'
import { EnumVisitingMode } from '@/api/customerVisit'

// 注册下拉枚举api
export default {
  GetRadixPointSignApi, // 获取小数位枚举列表
  GetSettleTypeApi, // 获取默认结算类型枚举列表
  GetSaleSystemDropdownListApi, // 获取营销体系下拉列表
  GetSaleSystemDropdownListV2, // 获取营销体系下拉列表(无数据隔离)
  StatusListApi, // 状态：启用，禁用
  GetEmployeeDutyListApi, // 职责列表
  GetDepartmentApi, // 部门列表
  GetMaritalStatusListApi, // 婚姻状况列表
  GetEducationLevelListApi, // 教育水平列表
  AdmingetUserDropdownList, // 用户下拉列表
  getSettleTypeEnum, // 结算方式枚举列表
  Adminemployeelist, // 员工下拉列表
  Adminbusiness_unitsale_arealist, // 销售区域下拉列表
  Adminbusiness_unitsale_grouplist, // 销售群体下拉列表
  Adminbusiness_unitcredit_levellist, // 信用等级下拉列表
  AdminsaleSystemgetSaleSystemDropdownList, // 所属营销体系
  AdminenumsettleType, // 默认结算类型
  AdminusergetSaleUserDropdownList, // 销售员下拉列表
  AdminuenumsupplierType, // 供应商类型下拉枚举列表
  AdminuenumwarehouseType, // 仓库类型下拉枚举
  getInfoBaseMeasurementUnitList, // 单位枚举列表
  getShowTypeEnum, // 控件展示类型枚举
  getTypeWarehouseList, // 原料类型枚举列表
  getTypeFabricList, // 布料类型枚举列表
  getTypeGreyFabricOrderList, // 坯布订单类型枚举列表
  getInfoProductLoomModelList, // 织机机型枚举
  getInfoProductGrayFabricColorList, // 胚布颜色枚举
  getInfoProductWeaveSpecificationList, // 织造规格枚举
  // rawmateriallist, // 原料资料类型枚举(弃用)
  rawmaterialMenu, // 原料资料枚举
  GetBusinessUnitListApi, // 往来单位枚举
  GetInfoBaseGreyFabricLevelListUseByOther, // 坯布等级
  GetInfoPurchaseInvoiceHeaderListUseByOther, // 发票抬头
  GetAuditStatusEnum, // 采购订单状态
  GetCustomerEnumList, // 客户列表
  GetGreyFabricInfoListUseByOthersMenu, // 坯布资料
  GetRoleDropdownList, // 获取角色枚举
  business_unitlist, // 往来单位枚举
  business_unitsupplierlist, // 供应商枚举列表
  BusinessUnitSupplierEnumlist, // 供应商枚举列表
  BusinessUnitSupplierEnumAll, // 供应商枚举列表
  getInfoBaseGreyFabricLevelList, // 坯布等级枚举
  getAuditStatusEnums, // 单据状态枚举
  getSourceWarehouseTypeEnum, // 坯布仓库来源类型枚举
  GetPurchaseGreyFabricItemList, // 获取坯布采购里面信息列表
  PrintTemplateType, // 打印类型
  getProductionNotifyOrderList, // 获取通知单列表
  GetGreyFabricInfoListUseByOthers, // 坯布名称和编号枚举
  // GetBusinessUnitReturnListApi, // 退货单位
  PlanTypeDropdownList, // 计划类型枚举
  GetProductionPlanOrderEsc, // 生产计划单枚举
  GetInfoProductPaymentTermList, // 付款期限枚举
  GetTypeFinishedProductColorEnumList, // 成品颜色种类枚举
  GetProductColorKindByProductId, // 成品颜色种类枚举根据成品id获取
  GetFinishProductDropdownList, // 成品资料枚举
  GetInfoBaseFinishedProductLevelEnumList, // 成品等级枚举
  GetFinishProductColorDropdownList, // 成品颜色枚举
  getInfoBaseMeasurementUnitEnumList, // 计量单位
  getInfoDyeingFinishingProcessDataEnumList, // 获取染整工艺编号枚举列表
  GetStockShowType, // 显示方式枚举
  GetInfoBaseRawMaterialLevelEnumList, // 原料等级下拉
  GetGawMaterialListMenu, // 原料库存枚举
  GetTypeFabricEnumList, // 布种类型
  GetPhysicalWarehouseBinListEnum, // 仓位枚举
  GetWarehouseGoodInTypeEnum, // 来源类型
  GetWarehouseGoodOutEnumTypeReverseIntMap, // 预约类型 预约单模块专属
  GetInfoSaleLogisticsCompanyEnumList, // 物流公司
  FactoryLogisticsEnum, // 加工厂
  GetEmployeeListEnum, // 员工列表获取仓管员下拉
  RoleAccessDataScope, // 数据权限
  GetProductionNotifyOrderDropdownList, // 生产通知单下拉
  GetWarehouseGoodOutChangeTypeEnum, // 变更信息的出货类型
  GetSaleLevelDropdownList, // 客户销售等级下拉
  GetInfoSaleTaxableItemEnumList, // 含税项目枚举列表
  GetSendProductTypeReverseIntMap, // 出货类型下拉枚举
  GetPostageItemsReverseIntMap, // 获取邮费项目枚举列表
  GetSettleTypeReverseIntMap, // 获取默认结算类型枚举列表
  Logistics_enum_list, // 根据客户id获取工厂物流
  GetDyeingColorDetailsByProduct, // 色号下拉枚举
  // getFinishProductColorDropdownList, // 色号枚举下拉列表
  // getInfoBaseFinishedProductLevelEnumList, // 成品等级枚举下拉列表
  getFinishProductColorDropdownList, // 色号枚举下拉列表
  getInfoSaleOrderCategoryEnumList, // 订单类别下拉枚举列表
  GetPhysicalWarehouseDropdownList, // 仓库资料下拉枚举列表
  GetDropdownListWithoutDS, // 调入仓库，所有调入的都必须用这个仓库
  GetInfoDyeingFinishingProgressEnumList, // 染整进度枚举列表
  GetGfmWarehouseTypeEnum, // 染整库存单据类型枚举
  GetSettleCycleApi, // 获取默认结算周期枚举列表
  CreditLevel, // 获取信用等级列表
  DnfChargingMethodEnum, // 获取染费收费枚举
  Getdnfchargingmethodenum, // 染费数量按枚举
  GetTypeSettleAccountsEnumList, // 结算方式下拉枚举
  GetProcessOutTypeTypeEnum, // 加工出仓类型
  CollectType, // 应收单类型枚举
  CollectStatus, // 收款状态枚举
  GetEnumWriteOffStatusReverseIntMap, // 核销状态枚举
  GetWarehouseGoodOutTypeEnum, // 获取所有出货类型枚举
  PushType, // 计划类型
  OweStatus, // 欠款状态
  GetDyeingColorDetailsByProductListEnum, // 染厂色号
  GetEnumBizUnitType, // 往来单位类型
  Quoterojectenum, // 计价项目枚举
  GetDictionaryDetailEnumListApi, // 字典列表
  GetStockStatusEnum, // 库存状态
  GetPlanTypeReverseIntMap, // 销售类型枚举
  GetRawMaterialColor, // 原料颜色
  GetRawMaterialEditOrderTypeEnum, // 原料库存单据类型
  GetListFactory, // 原料染厂颜色列表
  GetlistFactoryEnum, // 染纱厂色号枚举
  GetPayStatus, // 借款状态枚举
  GetTypeCapitalExpensesEnumList, // 资金费用类型枚举
  GetUserDropdownList, // 审核、修改人、创建人下拉
  RecExpType, // 收支类型枚举
  GetPayWayEnum, // 支付方式枚举
  GetPayStatusEnum, // 支付状态枚举
  GetReturnSupplierTypeReverseIntMap, // 退货供应商类型枚举
  GetTenantManagementStateEnum, // 租户管理状态枚举
  GetBusinessStatusEnumDropdownList, // 业务状态枚举
  GetInfoSaleSettlementMethodEnumList, // 结算方式枚举
  GetDataType,
  GetDNFTypeEnum,
  GetOrderTypeEnum,
  GetSrcTypeEnum,
  GetSituStatusReverseIntMap, // 获取销售计划单-进度状态(枚举)
  GetDnFSituStatusReverseIntMap, // 获取染整进度状态(枚举)
  GetListByDyelotNumber, // 获取缸号的下拉列表
  getPhysicalWarehouseListTreeEnum, // 获取树状枚举
  getPhysicalWarehouseList, // 获取缩水率
  GetInfoSaleShipmentTypeList, // 获取销售发货类型的列表数据
  GetGfmCheckOrderTypeEnum, // 单据类型
  GetGfmWarehouseOutTypeEnum,
  GetCarouselBannerJumpType, // 电子色卡轮播图跳转类型
  GetCarouselBannerStatus, // 电子色卡轮播图状态
  EnumVisitingMode,
}

// export const EnumQuery = {
//   'GetBusinessUnitListApi': {

//   }
// }
