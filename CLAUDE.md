# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Development
- `pnpm dev` - Start local development server (test environment)
- `pnpm dev:pre` - Start development server with pre-production environment
- `pnpm dev:prod` - Start development server with production environment
- `pnpm dev:tian2`, `pnpm dev:xmh1`, `pnpm dev:xmh2`, `pnpm dev:kdyb`, `pnpm dev:mes` - Start development with specific environment configurations

### Build & Preview
- `pnpm build` - Build for production
- `pnpm buildtest` - Build for development environment
- `pnpm buildpre` - Build for pre-production environment
- `pnpm buildprod` - Build for production environment
- `pnpm preview` - Preview production build locally

### Code Quality
- `pnpm lint` - Run ESLint to check code quality
- `pnpm lint:fix` - Run ESLint and automatically fix issues

### SVG Icons
- `pnpm svg` - Generate SVG icon components from src/assets/icons/svg

## Architecture Overview

### Frontend Stack
- **Framework**: Vue 3 with TypeScript
- **Build Tool**: Vite
- **UI Components**: Element Plus with VXE Table for advanced data grids
- **State Management**: Pinia with persistence
- **Router**: Vue Router with route-based navigation guards
- **Styling**: SCSS + Tailwind CSS + DaisyUI
- **HTTP Client**: Axios with request/response interceptors
- **Print System**: vue-plugin-hiprint for printing functionality

### Project Structure
- **src/api/**: API endpoints organized by feature modules
- **src/components/**: Reusable Vue components with detailed prop documentation
- **src/pages/**: Application pages organized by business domains
- **src/stores/**: Pinia state management modules
- **src/router/**: Route configuration with meta properties for permissions and caching
- **src/util/**: Utility functions including HTTP client and caching
- **src/use/**: Composable hooks for shared logic
- **src/common/**: Shared constants, formatting functions, and utilities

### Environment Configuration
The application supports multiple environments controlled by `.env.*` files:
- `.env.test` - Local development
- `.env.development` - Test environment
- `.env.pre` - Pre-production
- `.env.production` - Production
- `.env.kdyb`, `.env.mes`, `.env.tian2`, `.env.xmh1`, `.env.xmh2` - Specific deployment variants

### Data Formatting Standards
**Critical**: The application has specific data transformation requirements between frontend and backend:
- **金额 (Money)**: Multiply by 100 when sending to backend, divide by 100 when receiving
- **数量 (Quantity)**: Multiply by 10000 when sending to backend, divide by 10000 when receiving  
- **单价 (Unit Price)**: Multiply by 10000 when sending to backend, divide by 10000 when receiving
- **百分比 (Percentage)**: Multiply by 100 when sending to backend, divide by 100 when receiving

### Component Architecture

#### Table Component (src/components/Table.vue)
- Wrapper around VXE Table with pagination, sorting, filtering
- Configurable columns with built-in formatters for dates, prices, weights
- Field editing capabilities with permission controls
- Export functionality and custom cell renderers

#### Request Hook (src/use/useRequest.ts)
- Standardized API request handling with loading states
- Built-in pagination support
- Request caching and duplicate request prevention
- Filter and sort management

#### Permission System
- Route-based permissions using `v-has` directive for buttons
- Resources defined in system management with unique button codes
- Menu system tied to resource permissions

### Key Development Patterns

#### API Organization
- APIs grouped by business domain in src/api/
- Centralized in src/api/selectInit.ts for dropdown components
- Request caching and cancellation supported via configuration

#### Route Management
- Routes defined in src/router/routes.ts with meta properties
- `keepAlive` controls page caching behavior
- Breadcrumb navigation via `navName` meta property
- Auto-refresh logic for "新增" (add) and "新建" (create) pages

#### State Management
- Pinia stores with persistence plugin
- Global data, user info, router state, and caching stores
- Reactive updates across components

#### Component Props & Events
- Extensive prop documentation in README.md for each component
- Standardized event patterns (changeValue, select, submit, close)
- Slot-based customization for complex components

### Development Guidelines

#### Code Style
- ESLint configuration based on @antfu/eslint-config
- TypeScript strict mode enabled
- Vue 3 Composition API with `<script setup>`

#### Component Development
- Use existing component patterns from src/components/
- Follow the documented prop interfaces
- Leverage the Table component for data displays
- Use SelectDialog/SelectComponents for data selection

#### API Integration
- Use useRequest hook for standardized API calls
- Apply proper data transformations for money/quantity fields
- Implement request caching where appropriate
- Follow the established error handling patterns

#### Styling
- Prefer Tailwind utility classes
- Use SCSS for complex component styling
- Maintain responsive design principles
- Follow the established color and spacing conventions

### Print System
- Print templates managed in system settings
- Custom data formatters in src/components/PrintBtn/formatData/
- Print component supports direct data or API-based printing
- Template binding uses unique field identifiers