<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { cloneDeep } from 'lodash-es'
import { routeListStore } from '@/stores/routerList'

const router = useRouter()
const routerList = routeListStore()

const selectValue = ref('')
const selectOptions = ref<any>([])
const list = ref<any>([])

function flattenSubMenu(arr: any) {
  return arr?.reduce((flat: any, item: any) => {
    if (item.resource_router_name !== '') {
      flat.push({
        id: item?.id,
        name: item?.name,
        resource_router_name: item?.resource_router_name,
      })
    }
    if (item.sub_menu.length > 0)
      flat = flat.concat(flattenSubMenu(item.sub_menu))

    return flat
  }, [])
}

let errNum = 0

function setSelectOptions() {
  const arr = routerList.list
  selectOptions.value = cloneDeep(flattenSubMenu(arr))
  // 如果option还是空的，就200ms后再次执行
  if (selectOptions.value?.length === 0) {
    setTimeout(() => {
      // 如果执行了20次还是空的，就不再执行
      if (errNum < 10) {
        errNum++
        setSelectOptions()
      }
    }, 200)
  }
}

// 快捷键唤醒
const ElSelectRef = ref(null)

function keydownHandler(event: KeyboardEvent) {
  if (event.ctrlKey && event.key === 'k') {
    event.preventDefault()
    ElSelectRef.value?.focus()
  }
}

// 自定义过滤方法
function customFilter(query: string) {
  selectValue.value = query
  if (query !== '') {
    list.value = selectOptions.value.filter((item: any) => {
      return item.name.includes(query)
    })
  }
  return selectOptions.value
}

function handSelect(val: any) {
  router.push({
    name: val,
  })
  selectValue.value = ''
  list.value = []
}
function blurSelect() {
  selectValue.value = ''
  list.value = []
}
onMounted(async () => {
  await setSelectOptions()

  window.addEventListener('keydown', keydownHandler)
})
</script>

<template>
  <!-- 搜索框 -->
  <el-select
    ref="ElSelectRef" v-model="selectValue" style="width: 200px" :filter-method="customFilter" filterable
    clearable placeholder="请输入菜单名称" @change="handSelect" @blur="blurSelect"
  >
    <block v-if="list.length === 0 && selectValue === ''">
      <el-option v-for="item in selectOptions" :key="item.id" :label="item.name" :value="item.resource_router_name" />
    </block>
    <block v-else>
      <el-option v-for="item in list" :key="item.id" :label="item.name" :value="item.resource_router_name" />
    </block>
  </el-select>
</template>
