<!--
@description:展开的侧边菜单栏信息
  1:menu-item-level-last 最底层可点击--样式固定
  2:menu-item-level-1 第一级样式(非底层)+图标
  3:menu-item-level-2 第二级样式(非底层)
 -->
<script lang="ts" setup name="SubMenu">
interface ChildProps {
  list: any
  level: number // 层级
}

const props = withDefaults(defineProps<ChildProps>(), {
  list: {},
  level: 1,
})

const emit = defineEmits(['handChild'])

// 判断当前是否为最底层-可点击跳转
function isLastMenu(data: any) {
  return !data.sub_menu || data.sub_menu.length === 0
}

// 点击
function handleClick(item: any) {
  isLastMenu(item) && emit('handChild', item) // 最底层点击
}
</script>

<template>
  <template v-for="(item, index) in props.list" :key="item">
    <div
      class="menu-item-wrapper"
    >
      <div
        class="menu-item" :class="[
          `menu-item-level-${level}`,
          { 'menu-item-level-last': isLastMenu(item) },
          { 'menu-item-level-last-3': level === 3 && isLastMenu(item) },
          { 'last-span': isLastMenu(item) && index === props.list.length - 1 }]"
        @click="handleClick(item)"
      >
        <svg-icon
          v-if="level === 1 && !(isLastMenu(item)) && item?.avatar_url" v-has="'GreyFabricInformation_status'"
          :name="item?.avatar_url" size="22px" class="mr-[4px]" color="#0E7EFF"
        />
        <span class="menu-item-title">{{ item?.name }}</span>
      </div>
    </div>
    <template v-if="!isLastMenu(item)">
      <SubMenu :list="item.sub_menu" :level="props.level + 1" @hand-child="handleClick" />
    </template>
  </template>
</template>

<style scoped lang="scss">
.menu-item-wrapper{
  // display: inline-block;
  writing-mode: horizontal-tb;
}
.menu-item {
  display: flex;
  align-items: center;
  color: #333;
  margin-right: 20px;
  min-width: 150px;
  width: auto;
  margin-top: 5px;
  box-sizing: border-box;
  // max-width: max-content;
  // min-width: 0;
  // width:160px;
  // flex:1;
  // writing-mode: horizontal-tb;
}

/* 一级菜单 */
.menu-item-level-1 {
  color: #0E7EFF;
  font-weight: bold;

  // 非最后层级
  &:not(.menu-item-level-last) {
    margin-bottom: 10px;
    // margin-top: 18px;
  }
}

/* 二级菜单 */
.menu-item-level-2 {
  color: rgba(0, 0, 0, 0.88);
  margin-left: 4px;

  // 非最后层级
  &:not(.menu-item-level-last) {
    font-weight: bold;
  }
}

/* 底层菜单 */
.menu-item-level-last {
  cursor: pointer;
  color: rgba(0, 0, 0, 0.45);
  font-weight: normal;
  margin-left: 4px;

  .menu-item-title {
    border-bottom: 1px solid transparent;
  }

  &:hover {
    .menu-item-title {
      color: #0E7EFF;
      border-color: #0E7EFF;
    }
  }
}

// 一个层级模块的最后一个
.last-span{
  margin-bottom: 15px;
}

/* 底层菜单+第三级 */
.menu-item-level-last-3 {
  // padding-left: 34px;
}
</style>
