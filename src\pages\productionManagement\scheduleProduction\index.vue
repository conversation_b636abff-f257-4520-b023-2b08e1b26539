<script setup lang="ts">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { BusinessUnitIdEnum } from '@/common/enum'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import { getProductionNotice } from '@/api/productionNotice'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { debounce, getFilterData } from '@/common/util'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import MachineSchedulingDialog from '@/pages/productionManagement/productionNotice/components/MachineSchedulingDialog.vue'

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeProduceNotify,
  dataType: PrintDataType.Product,
})

const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_name: '',
  raw_code: '',
})
const state = reactive<any>({
  tableData: [{}],
  filterData: {
    order_no: '',
    production_plan_order_no: '',
    weaving_mill_id: '',
    status: [],
  },
  multipleSelection: [],
})
const {
  fetchData: ApiCustomerList,
  data: datalist,
  total,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
}: any = getProductionNotice()
// 首次加载数据
onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})
// 生产通知单表格列配置
const columnList = ref([
  {
    sortable: true,
    field: 'order_no',
    title: '织厂名称',
    soltName: 'order_no',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'production_plan_order_no',
    title: '生产通知单',
    soltName: 'production_plan_order_no',
    fixed: 'left',
    width: '8%',
  },
  {
    sortable: true,
    field: 'sale_system_name',
    title: '排产单号',
    fixed: 'left',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaving_mill_name',
    title: '坯布名称',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'weaving_mill_order_follower_name',
    title: '纱批',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '机台',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'sale_user_name',
    title: '合同号',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_code',
    title: '合同备注',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'scheduling_roll',
    title: '布种后整',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'receipt_grey_fabric_date',
    title: '原料品牌',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'order_remark',
    title: '原料纱名',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '进仓数量',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '进仓条数',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '称重数量',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '称重条数',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '验布数量',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '验布条数',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '出仓数量',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '出仓条数',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '库存数量',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '库存条数',
    minWidth: 150,
    isDate: true,
  },
  {
    sortable: true,
    field: 'notify_date',
    title: '打印时间',
    minWidth: 100,
    is_date: true,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '打印人',
    minWidth: 150,
  },
  {
    field: 'creator_name',
    title: '创建时间',
    isDate: true,
    width: 100,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '创建人',
    minWidth: 150,
  },
  {
    field: 'auditor_name',
    soltName: 'status',
    showOrder_status: true,
    fixed: 'right',
    title: '打印状态',
    width: 100,
  },
])
const columnList1 = ref([
  {
    field: 'audit_date',
    title: '机台',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '条形码',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '卷号',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '坯布等级',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '质量备注',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '纱批',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '原料批号',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '纱牌',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '进仓数量',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '进仓条数',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '称重数量',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '称重条数',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '验布数量',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '验布条数',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '出仓数量',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '出仓条数',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '库存数量',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '库存条数',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '查布时间',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '打印人',
    minWidth: 150,
  },
  {
    field: 'bar_code',
    title: '打印时间',
    minWidth: 150,
  },
])
// 获取数据
async function getData() {
  await ApiCustomerList(
    getFilterData({
      ...state.filterData,
      status: state.filterData.status.join(','),
    }),
  )
}
// 实时过滤数据
watch(
  () => state.filterData,
  debounce(() => {
    getData()
  }, 400),
  {
    deep: true,
  },
)
// 生产通知单表格列配置
const tableConfig = ref({
  fieldApiKey: 'ScheduleProduction',
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '140',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})
// 生产通知单表格列配置
const tableConfig1 = ref({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '100',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
})
function handlePrint() {

}

// 机台排产编辑相关
const showEditDialog = ref(false)
const editRowData = ref<any>({})

function handleEdit(row: any) {
  editRowData.value = { ...row }
  showEditDialog.value = true
}

// 处理编辑保存
function handleEditSave(data: any) {
  console.log('保存编辑数据:', data)
  // 这里可以调用API保存数据
  showEditDialog.value = false
  // 重新获取数据
  getData()
}
</script>

<template>
  <div class="list-page">
    <FildCard :tool-bar="false" title="">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="排查日期:">
          <template #content>
            <SelectDate v-model="state.filterData.devierDate" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销部门:">
          <template #content>
            <SelectComponents v-model="state.filterData.default_sale_system_id" label-field="name" value-field="id" api="GetSaleSystemDropdownListApi" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.weaving_mill_id"
              api="business_unitlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.knittingFactory,
                name: componentRemoteSearch.name,
              }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="机台号:">
          <template #content>
            <el-input v-model="state.filterData.machine_number" clearable placeholder="机台号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="材料批号:">
          <template #content>
            <el-input v-model="state.filterData.machine_number" clearable placeholder="机台号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布飞状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              multiple
              api="getAuditStatusEnums"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织造状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.status"
              multiple
              api="getAuditStatusEnums"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <Table
        ref="tableRef"
        :config="tableConfig"
        :table-list="state.tableData"
        :column-list="columnList"
      >
        <template #print_status="{ row }">
          <div class="flex items-center">
            <div :class="row.status === 1 ? 'yuan' : 'yuan_red'" />
            <div :class="row.status === 1 ? 'yuan_font' : 'yuan_font_active'">
              {{ row?.status === 1 ? "启用" : "禁用" }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <PrintPopoverBtn
            :id="row.id"
            style="width: auto"
            print-btn-text="打印"
            print-btn-type="text"
            api="getProductionNotifyOrder"
            :options="options"
          />
          <el-link type="primary" class="ml-2" :underline="false" @click="handleEdit(row)">
            编辑
          </el-link>
        </template>
      </Table>
    </FildCard>
    <FildCard title="" class="table-card-bottom" :tool-bar="false">
      <div class="mb-2">
        <el-button type="primary" plain @click="handlePrint">
          打印布飞
        </el-button>
        <el-checkbox v-model="state.isShow" class="ml-2">
          显示未打印条码
        </el-checkbox>
      </div>
      <Table
        ref="tableRef"
        :config="tableConfig1"
        :table-list="state.tableData"
        :column-list="columnList1"
      >
        <template #operate="{ row }">
          <PrintPopoverBtn
            :id="row.id"
            style="width: auto"
            print-btn-text="打印"
            print-btn-type="text"
            api="getProductionNotifyOrder"
            :options="options"
          />
        </template>
      </Table>
    </FildCard>

    <!-- 机台排产编辑弹窗 -->
    <MachineSchedulingDialog
      v-model="showEditDialog"
      mode="edit"
      :production-data="editRowData"
      @save="handleEditSave"
    />
  </div>
</template>

<style scoped>

</style>
